const axios = require('axios');
require('dotenv').config();

const apiKey = process.env.freshwork_api_key;
const domain = process.env.freshwork_domain;

const headers = {
    'Authorization': `Token token=${apiKey}`,
    'Content-Type': 'application/json'
};

exports.searchContact = async (email) => {
  try {
    const response = await axios.get(`${domain}/api/search?q=${email}&include=contact`, {
      headers
    });
    // console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching employees:', error);
    throw error;
  }
}

exports.getContact = async (id, params) => {
  try {
    const response = await axios.get(`${domain}/api/contacts/${id}`, {
      params,
      headers
    });
    // console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching employees:', error);
    throw error;
  }
}

exports.createContact = async (contact) => {
  try {
    const response = await axios.post(`${domain}/api/contacts`, contact, {
      headers
    });
    // console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching employees:', error);
    throw error;
  }
}

exports.updateContact = async (id, contact) => {
  try {
    const response = await axios.put(`${domain}/api/contacts/${id}`, contact, {
      headers
    });
    // console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating contact:', error);
    throw error;
  }
}

exports.bulkUpsert = async (contact) => {
  try {
    const response = await axios.post(`${domain}/api/contacts/bulk_upsert`, contact, {
      headers
    });
    // console.log(response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating contact:', error);
    throw error;
  }
}
