const { searchContact, createContact1, updateContact1, getContact1 } = require("../api/freshworkApis");

exports.talkCampaign = async (req, res) => {
    const adResponse = req.body;
    // const adResponse = {
    //     lead_id: 'TeSter-123-ABCDEFGHIJKLMNOPQRSTUVWXYZ-abcdefghijklmnopqrstuvwxyz-0123456789-AaBbCcDdEeFfGgHhIiJjKkLl',
    //     user_column_data: [
    //         {
    //             column_name: 'Full Name',
    //             string_value: 'FirstName LastName',
    //             column_id: 'FULL_NAME'
    //         },
    //         {
    //             column_name: 'User Phone',
    //             string_value: '+91 9360236783',
    //             column_id: 'PHONE_NUMBER'
    //         },
    //         {
    //             column_name: 'City',
    //             string_value: 'Mountain View',
    //             column_id: 'CITY'
    //         },
    //         {
    //             column_name: 'Postal Code',
    //             string_value: '94043',
    //             column_id: 'POSTAL_CODE'
    //         },
    //         {
    //             column_name: 'Work Email',
    //             string_value: '<EMAIL>',
    //             column_id: 'WORK_EMAIL'
    //         },
    //         {
    //             column_name: 'Company Name',
    //             string_value: 'CompanyName',
    //             column_id: 'COMPANY_NAME'
    //         },
    //         {
    //             string_value: 'Example answer',
    //             column_id: 'what_product_are_you_interested_in?'
    //         }
    //     ],
    //     api_version: '1.0',
    //     form_id: 238118026918,
    //     campaign_id: 10000000000,
    //     google_key: 'Elgi2025',
    //     is_test: true,
    //     gcl_id: 'TeSter-123-ABCDEFGHIJKLMNOPQRSTUVWXYZ-abcdefghijklmnopqrstuvwxyz-0123456789-AaBbCcDdEeFfGgHhIiJjKkLl',
    //     adgroup_id: 20000000000,
    //     creative_id: 30000000000
    // }
    logger.info({ googleResponse: adResponse });
    const currentDate = new Date().toISOString();
    const condition = ["", null, undefined, "null", "undefined"];
    function GetValue(column_id) {
        const data = adResponse.user_column_data.find(x => x.column_id == column_id).string_value;
        if (data && !condition.includes(data.trim())) {
            return data;
        }
        return "";
    }
    const editPhoneNumber = (phone) => {
        phone = phone.trim();
        if (phone.startsWith("+91")) {
            phone = phone.slice(3);
        }
        phone = phone.replace(/\D/g, '');
        return phone;
    };

    const payload = {
        name: GetValue('FULL_NAME'),
        phone: editPhoneNumber(GetValue('PHONE_NUMBER')),
        email: GetValue('WORK_EMAIL'),
        company: GetValue('COMPANY_NAME'),
        city: GetValue('CITY'),
        zip: GetValue('POSTAL_CODE'),
        product: GetValue('what_product_are_you_interested_in?')
    }

    const searchEmail = await searchContact(payload.email);

    if (searchEmail.length === 0) {
        const contactCreation = createContact({
            first_name: payload.name,
            mobile_number: payload.phone,
            email: payload.email,
            city: payload.city,
            zipcode: payload.zip,
            custom_field: {
                cf_company_name: payload.company,
                cf_what_are_you_looking_for: payload.product,
                cf_platform: "Google ads",
                cf_updated_by_server: currentDate
            }
        })
        console.log(contactCreation);
        logger.info({ contactCreation: "Contact created successfully!" });
        res.send("Contact created successfully!");
    }
    else {
        const contactId = searchEmail[0].id;
        const { contact: { custom_field: { cf_updated_by_server } } } = await getContact(contactId);
        console.log(cf_updated_by_server);
        let updatedByServer;
        if (cf_updated_by_server && !condition.includes(cf_updated_by_server.trim())) {
            updatedByServer = cf_updated_by_server;
        } else {
            return;
        }
        const givenDate = new Date(updatedByServer);
        const differenceInMs = Math.abs(currentDate - givenDate);
        const differenceInDays = differenceInMs / (1000 * 60 * 60 * 24)
        if (differenceInDays < 3) {
            console.log("✅ The date is within 3 days.");
             logger.info({ message: "The date is within 3 days." });
            return;
        } else {
            const updateContact = await updateContact({
                id: searchEmail[0].id,
                first_name: payload.name,
                mobile_number: payload.phone,
                email: payload.email,
                city: payload.city,
                zipcode: payload.zip,
                custom_field: {
                    cf_company_name: payload.company,
                    cf_what_are_you_looking_for: payload.product,
                    cf_platform: "Google ads",
                    cf_adcampaign: "",
                    cf_updated_by_server: currentDate
                }
            })
            console.log(updateContact);
            console.log("❌ The date is NOT within 3 days.");
             logger.info({ message: "Contact updated successfully!" });
            res.send("Contact created successfully!");
        }
    }
}


// exports.airCompressorCampaign = async (req, res) => {
//     res.send("Hello World!");
// }