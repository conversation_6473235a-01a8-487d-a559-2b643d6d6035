const createLogger = require("../utils/logger");
const logger = createLogger("indiaMartController");
const {
  searchContact,
  getContact,
  createContact,
  updateContact,
  bulkUpsert

} = require("../api/freshworkApis");
const { indiaMartApi } = require("../api/indiaMartApi");

const condition = ["", null, undefined, "null", "undefined"];
const isValidField = (field) => field && !condition.includes(field.trim());

exports.contactSync = async () => {
  try {
    const date = new Date();
    const to_date = date.toISOString().slice(0, 10);
    const from_date = date.toISOString().slice(0, 10);
    const {RESPONSE: response} = await indiaMartApi({
      from_date,
      to_date
    });
    logger.info({ response });
    console.log({response})
    let contacts = [];

          for (const detail of response) {
            logger.info({detail: JSON.stringify(detail)});
            const email = detail.SENDER_EMAIL || detail.SENDER_MOBILE + "@noemail.com";
            logger.info({ email });

            // Check if contact exists before creating or updating
            const contactExists = await searchContact(email);
            logger.info({ contactExists });
            if (contactExists.length > 0) {
              logger.info(`Contact already exists: ${detail.SENDER_NAME}, Contact ID: ${contactExists[0].id}`);
              const requester = contactExists.find((contact) => contact.email === email) || contactExists[0];
              logger.info({requester})
              const updated = requester.updated_at.split('T')[0] !== (new Date().toISOString()).split('T')[0];
              logger.info("Is Updated?", updated);
              if (!updated) continue; // Skip if already updated today
            }

            const data = {
              first_name: detail.SENDER_NAME,
              mobile_number: detail.SENDER_MOBILE,
              email,
              lead_source_id: 70000409914,
              contact_status_id: 70000194173,
              cf_what_are_you_looking_for:"Product Inquiry",
              custom_field: {}
            };

            const condition = ["", null, undefined, "null", "undefined"];
            const isValidField = (field) => field && !condition.includes(field.toString().trim());

            if (isValidField(detail.SUBJECT)) data.description = detail.SUBJECT;
            if (isValidField(detail.SENDER_PHONE)) data.work_number = detail.SENDER_PHONE;
            if (isValidField(detail.SENDER_CITY)) data.city = detail.SENDER_CITY;
            if (isValidField(detail.QUERY_MESSAGE)) data.custom_field.cf_description = detail.QUERY_MESSAGE;
            if (isValidField(detail.SENDER_COMPANY)) data.custom_field.cf_company_name = detail.SENDER_COMPANY;
            if (isValidField(detail.SENDER_STATE)) data.custom_field.cf_state = detail.SENDER_STATE;
            if (isValidField(detail.SENDER_ADDRESS)) data.address = detail.SENDER_ADDRESS;
            if (isValidField(detail.QUERY_PRODUCT_NAME)) data.custom_field.cf_product_model = detail.QUERY_PRODUCT_NAME;
            contacts.push({emails:email, data});
            
            // try {
            //   console.log({ payload: JSON.stringify({contact}) });
            //   const { response: contactResponse } = await $request.invokeTemplate(
            //     "updateContact",
            //     { context: { id: contactExists[0].id }, body: JSON.stringify({contact}) }
            //   );
            //   console.log("Contact Updated:", contactResponse);
            // } catch (error) {
            //   console.error("Error processing or updating contact:", {
            //     name: payload.name,
            //     error: error.response
            //   });
            // }
  
           
          }

    logger.info({
      length: contacts.length,
      contacts: JSON.stringify(contacts),
    });

    if (contacts.length == 0) return logger.info("No contacts to create.");

    const { response: contactResponse } = await bulkUpsert(JSON.stringify({ contacts }));
    logger.info("Contact created:", contactResponse);

    
  } catch (error) {
    console.error("Error creating contact:", error);
    logger.error("Error creating contact:", error);
    // ({ error: "Internal server error" });
  }
};
