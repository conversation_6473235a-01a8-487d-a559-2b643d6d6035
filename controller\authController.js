const db = require("../database");

exports.login = async (req, res) => {
    const { email, password } = req.body;

    try {
        const result = await db.loginUser(email, password);

        if (!result.success) {
            return res.status(400).json({ message: result.message });
        }

        res.json(result);
    } catch (error) {
        res.status(500).json({ message: "Server error", error: error.message });
    }
};