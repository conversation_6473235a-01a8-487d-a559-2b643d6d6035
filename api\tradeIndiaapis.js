const axios = require('axios');
require('dotenv').config();

const trade_domain = process.env.trade_domain;
const trade_key = process.env.trade_key;
const trade_userid = process.env.trade_userid;
const trade_profile_id = process.env.trade_profile_id;

const headers = {
    'Content-Type': 'application/json',
    'Accept': '*/*'
}

exports.tradeIndiaApi = async ({from_date, to_date, limit, page_no}) => {
  try {
    const response = await axios.get(`${trade_domain}`, {
      params: {
        key: trade_key,
        userid: trade_userid,
        profile_id: trade_profile_id,
        from_date,
        to_date,
        limit,
        page_no
      },
      headers
    });
    return response.data;
  } catch (error) {
    console.error('Error creating contact:', error);
    throw error;
  }
}
