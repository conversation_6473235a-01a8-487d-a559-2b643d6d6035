const axios = require('axios');
require('dotenv').config();

const india_mart_domain = process.env.india_mart_domain;
const glusr_crm_key = process.env.glusr_crm_key;

const headers = {
    'Content-Type': 'application/json',
    'Accept': '*/*'
}

exports.indiaMartApi = async ({from_date, to_date}) => {
  try {
    console.log(`${india_mart_domain},${glusr_crm_key}`)
    const response = await axios.get(`${india_mart_domain}`, {
      params: {
        glusr_crm_key,
        from_date,
        to_date
      },
      headers
    });
    console.log(response.data)
    return response.data;
  } catch (error) {
    console.error('Error creating contact:', error);
    throw error;
  }
}
