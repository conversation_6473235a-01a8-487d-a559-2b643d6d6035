const createLogger = require("../utils/logger");
const logger = createLogger("tradeIndiaController");

const {
  searchContact,
  getContact,
  createContact,
  updateContact,
  bulkUpsert

} = require("../api/freshworkApis");
const { tradeIndiaApi } = require("../api/tradeIndiaapis");
const database = require("../database");

const condition = ["", null, undefined, "null", "undefined"];
const isValidField = (field) => field && !condition.includes(field.trim());

exports.contactSync = async () => {
  try {
    const date = new Date();
    const to_date = date.toISOString().slice(0, 10);
    const from_date = date.toISOString().slice(0, 10);
    const response = await tradeIndiaApi({
      from_date,
      to_date,
      limit: 50,
      page_no: 1,
    });
    logger.info({ response });
    let contacts = [];

    for (const detail of response) {
      //   console.log({ detail: JSON.stringify(detail) });
      const email = detail.sender_email || detail.sender_mobile + "@noemail.com";
      await database.createData({email, details: JSON.stringify(detail), status: "pending", source: "tradeIndia"});
      const contactExists = await searchContact(email);
      logger.info({ contactExists });
      if (contactExists.length > 0) {
        logger.info(
          `Contact already exists: ${detail.sender_name}, Contact ID: ${contactExists[0].id}`
        );
        const requesterID = contactExists.find((contact) => contact.email === email)?.id || contactExists[0].id;
        if (requesterID) {
          const {contact: contactDetails} = await getContact(requesterID, {
            include: "updater"
          });
          console.log({contactDetails})
          const { updated_at, updater_id, custom_field } = contactDetails;
          if(custom_field.cf_updated_by_server && custom_field.cf_updated_by_server.slice(0, 10) === new Date().toISOString().slice(0, 10)){
            logger.info(`Contact already updated: ${detail.sender_name} Contact ID: ${requesterID}`);
            await database.updateDate({email, status: "Already updated"});
            continue;
          }
          logger.info({contactDetails})

          // const { updated_at } = contactDetails


          logger.info("Contact updated:", contactDetails);
          const updatedContact = {
            first_name: detail.sender_name,
            mobile_number: detail.sender_mobile,
            email,
            lead_source_id: 70001227616,
            cf_what_are_you_looking_for: "Product Inquiry",
            custom_field: {
              cf_updated_by_server: new Date().toISOString(),
            }
          };
          try {
            const updatedContactResponse = await updateContact(
              requesterID,
              updatedContact
            );
            logger.info("Contact updated:", updatedContactResponse);
          } catch (error) {
            logger.error("Error updating contact:", error);
          }
        }
      }
      const contact = {
        first_name: detail.sender_name,
        mobile_number: detail.sender_mobile,
        email,
        lead_source_id: 70001227616,
        cf_what_are_you_looking_for: "Product Inquiry",
        custom_field: {},
      };


      if (isValidField(detail.subject)) contact.description = detail.subject;
      if (isValidField(detail.landline_number))
        contact.work_number = detail.landline_number;
      if (isValidField(detail.sender_city)) contact.city = detail.sender_city;
      if (isValidField(detail.sender_state))
        contact.custom_field.cf_state = detail.sender_state;
      if (isValidField(detail.sender_country))
        contact.custom_field.cf_country = detail.sender_country;
      if (isValidField(detail.message) && detail.message.length > 200)
        contact.custom_field.cf_form_comments = detail.message;
      if (isValidField(detail.message) && detail.message.length < 200)
        contact.custom_field.cf_comments = detail.message;
      if (isValidField(detail.sender_co))
        contact.custom_field.cf_company_name = detail.sender_co;

      logger.info({ emails: email, data: contact });
      contacts.push({ emails: email, data: contact });
    }


    logger.info({
      length: contacts.length,
      contacts: JSON.stringify(contacts),
    });


    if (contacts.length == 0) return logger.info("No contacts to create.");

    // const { response: contactResponse } = await bulkUpsert(JSON.stringify({ contacts }));
    // logger.info("Contact created:", contactResponse);

    // if (contacts.length < 100) {
    //   try {
    //     const { response: contactResponse } = await bulkUpsert(JSON.stringify({ contacts }));
    //     console.log("Contact created:", contactResponse);
    //   } catch (error) {
    //     console.error("Error creating contact for:", payload, error);
    //   }
    // } else {
    //   // console.log("Contact limit reached, skipping contact creation.");
    //   const { response: contactResponse } = await bulkUpsert({
    //     contacts: contacts.slice(0, 99)
    //   });
    //   console.log("Contact created:", contactResponse);
    //   const { response: contactResponse1 } = await bulkUpsert({
    //     contacts: contacts.slice(99)
    //   });
    //   console.log("Contact created:", contactResponse1);
    // }
  } catch (error) {
    logger.error("Error creating contact:", error);
    // res.status(500).json({ error: "Internal server error" });
  }
};
